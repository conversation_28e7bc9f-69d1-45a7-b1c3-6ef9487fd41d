# BLE Scanner MCP Tool

This document describes the BLE (Bluetooth Low Energy) scanner tool that has been added to the MCP server.

## Overview

The BLE scanner tool allows the device to scan for nearby Bluetooth Low Energy devices and return information about discovered devices including their MAC addresses, signal strength (RSSI), and device names (if available).

## Tool Details

**Tool Name:** `self.ble.scan`

**Description:** Scan for nearby Bluetooth Low Energy (BLE) devices.

**Parameters:**
- `duration` (integer, optional): Scan duration in seconds (1-30, default: 10)

**Return Value:** A JSON object containing:
- `success` (boolean): Whether the scan was successful
- `devices` (array): List of discovered devices with their information
- `count` (integer): Number of devices found
- `message` (string): Error message if scan failed

## Usage Example

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "self.ble.scan",
    "arguments": {
      "duration": 15
    }
  }
}
```

## Response Example

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "{\"success\": true, \"devices\": [\"Device: aa:bb:cc:dd:ee:ff, RSSI: -45 dBm, Name: MyDevice\", \"Device: 11:22:33:44:55:66, RSSI: -67 dBm\"], \"count\": 2}"
      }
    ],
    "isError": false
  }
}
```

## Implementation Details

### Bluetooth Configuration

The following Bluetooth configurations have been added to `sdkconfig.defaults`:

```
CONFIG_BT_ENABLED=y
CONFIG_BT_BLUEDROID_ENABLED=y
CONFIG_BT_CLASSIC_ENABLED=n
CONFIG_BT_BLE_ENABLED=y
CONFIG_BT_CONTROLLER_ENABLED=y
CONFIG_BT_CONTROLLER_ONLY=n
CONFIG_BT_NIMBLE_ENABLED=n
```

### CMakeLists.txt Changes

The `bt` component has been added to the `REQUIRES` section in `main/CMakeLists.txt`:

```cmake
idf_component_register(SRCS ${SOURCES}
                    EMBED_FILES ${LANG_SOUNDS} ${COMMON_SOUNDS}
                    INCLUDE_DIRS ${INCLUDE_DIRS}
                    REQUIRES bt
                    WHOLE_ARCHIVE
                    )
```

### Key Features

1. **Automatic BLE Initialization**: The BLE stack is initialized automatically when the tool is first used
2. **Scan Parameter Configuration**: Uses active scanning with optimized parameters for device discovery
3. **Device Information Extraction**: Extracts MAC address, RSSI, and device name from advertising data
4. **Thread-Safe Operation**: Uses semaphores to ensure only one scan can run at a time
5. **Timeout Protection**: Includes timeout handling to prevent hanging scans
6. **Error Handling**: Comprehensive error handling for all BLE operations

### Memory Considerations

- The BLE stack requires additional RAM and flash memory
- Scan results are stored in a vector that grows with the number of discovered devices
- The implementation uses a semaphore for synchronization

### Limitations

1. Only scans for BLE devices (not classic Bluetooth)
2. Maximum scan duration is limited to 30 seconds
3. Only one scan can be active at a time
4. Device names are only available if included in advertising data

## Troubleshooting

### Common Issues

1. **"Failed to initialize BLE"**: Check that Bluetooth is enabled in the configuration
2. **"BLE scan already in progress"**: Wait for the current scan to complete before starting a new one
3. **"Scan timeout"**: The scan may have taken longer than expected; try reducing the duration

### Debug Logging

Enable BLE debug logging by setting the log level for the "BLE_SCANNER" tag:

```c
esp_log_level_set("BLE_SCANNER", ESP_LOG_DEBUG);
```

## Future Enhancements

Potential improvements that could be added:

1. **Filtering Options**: Add parameters to filter devices by RSSI threshold or device name
2. **Extended Information**: Extract more information from advertising data (services, manufacturer data)
3. **Continuous Scanning**: Option for continuous scanning with periodic updates
4. **Device Tracking**: Track device appearances/disappearances over time
5. **Connection Capabilities**: Extend to support connecting to discovered devices
