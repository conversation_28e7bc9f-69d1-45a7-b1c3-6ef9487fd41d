/*
 * SPDX-FileCopyrightText: 2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Unlicense OR CC0-1.0
 */
#ifndef GAP_SVC_H
#define GAP_SVC_H

/* Includes */
/* NimBLE GAP APIs */
#include "host/ble_gap.h"
#include "services/gap/ble_svc_gap.h"

/* Defines */
#define BLE_GAP_APPEARANCE_GENERIC_TAG 0x0200
#define BLE_GAP_URI_PREFIX_HTTPS 0x17
#define BLE_GAP_LE_ROLE_PERIPHERAL 0x00

/* Scan result structure */
struct ble_scan_result {
    uint8_t addr[6];
    uint8_t addr_type;
    int8_t rssi;
    char name[32];
    bool has_name;
};

/* Scan callback function type */
typedef void (*ble_scan_callback_t)(struct ble_scan_result *results, size_t count);

/* Public function declarations */
void adv_init(void);
int gap_init(void);
int ble_scanner_start(uint32_t duration_ms, ble_scan_callback_t callback);
int ble_scanner_stop(void);
bool ble_scanner_is_scanning(void);

#endif // GAP_SVC_H
